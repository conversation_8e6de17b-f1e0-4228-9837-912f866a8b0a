import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ExternalLink, Calendar, MapPin } from "lucide-react"

export default function Projects() {
  const projects = [
    {
      title: "International Medical Conference 2024",
      category: "Conference",
      image: "/placeholder.svg?height=300&width=400",
      description:
        "A comprehensive medical conference featuring leading healthcare professionals from around the world.",
      stats: { attendees: "500+", speakers: "50+", days: "3" },
      location: "Mumbai, India",
      date: "March 2024",
    },
    {
      title: "Pharmaceutical Innovation Expo",
      category: "Exhibition",
      image: "/placeholder.svg?height=300&width=400",
      description: "Showcasing the latest innovations in pharmaceutical technology and research.",
      stats: { exhibitors: "200+", visitors: "10,000+", days: "4" },
      location: "Delhi, India",
      date: "February 2024",
    },
    {
      title: "Global Tech Summit",
      category: "Corporate Event",
      image: "/placeholder.svg?height=300&width=400",
      description: "Annual technology summit bringing together industry leaders and innovators.",
      stats: { attendees: "800+", speakers: "30+", days: "2" },
      location: "Bangalore, India",
      date: "January 2024",
    },
    {
      title: "Healthcare Leadership Workshop",
      category: "Workshop",
      image: "/placeholder.svg?height=300&width=400",
      description: "Intensive leadership development program for healthcare executives.",
      stats: { participants: "100+", sessions: "12", days: "2" },
      location: "Chennai, India",
      date: "December 2023",
    },
    {
      title: "Digital Health Conference",
      category: "Conference",
      image: "/placeholder.svg?height=300&width=400",
      description: "Exploring the future of digital health technologies and telemedicine.",
      stats: { attendees: "600+", speakers: "40+", days: "3" },
      location: "Pune, India",
      date: "November 2023",
    },
    {
      title: "Biotech Innovation Showcase",
      category: "Exhibition",
      image: "/placeholder.svg?height=300&width=400",
      description: "Premier biotechnology exhibition featuring cutting-edge research and products.",
      stats: { exhibitors: "150+", visitors: "8,000+", days: "3" },
      location: "Hyderabad, India",
      date: "October 2023",
    },
  ]

  return (
    <section id="projects" className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 bg-orange-100 rounded-full px-6 py-2 text-sm font-medium text-orange-600 mb-6">
            <Calendar className="w-4 h-4" />
            Our Recent Projects
          </div>
          <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-8">
            Featured{" "}
            <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">Projects</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Explore our portfolio of successful events that have brought together industry leaders, innovators, and
            professionals from around the world. Each project showcases our commitment to excellence and attention to
            detail.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
          {projects.map((project, index) => (
            <Card
              key={index}
              className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 overflow-hidden bg-white border-0 animate-fade-in-up"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden">
                  <Image
                    src={project.image || "/placeholder.svg"}
                    alt={project.title}
                    width={400}
                    height={300}
                    className="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4 bg-gradient-to-r from-orange-500 to-rose-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                    {project.category}
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Button
                    size="sm"
                    className="absolute bottom-4 right-4 bg-white/90 text-gray-900 hover:bg-white opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Details
                  </Button>
                </div>

                <div className="p-8">
                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {project.location}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {project.date}
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors duration-300">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{project.description}</p>

                  <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
                    {Object.entries(project.stats).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className="text-lg font-bold text-orange-600">{value}</div>
                        <div className="text-xs text-gray-500 capitalize">{key}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16">
          <Button
            size="lg"
            className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white px-10 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            View All Projects
            <ExternalLink className="ml-2 w-5 h-5" />
          </Button>
        </div>
      </div>
    </section>
  )
}
